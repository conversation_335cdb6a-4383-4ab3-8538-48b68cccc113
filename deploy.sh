#!/bin/bash

# Exit on error
set -e

# Add domain to /etc/hosts for local development if it doesn't exist
if ! grep -q "hrbot.merai.app" /etc/hosts; then
    echo "127.0.0.1 hrbot.merai.app" | sudo tee -a /etc/hosts
fi

# Stop any existing containers
echo "Stopping any existing containers..."
docker-compose down || true

# Clean up any dangling images
echo "Cleaning up dangling images..."
docker image prune -f

# Build and start the containers
echo "Building and starting containers..."
docker-compose up -d --build

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 10

# Check container status
echo "Checking container status..."
docker ps -a

echo ""
echo "Frontend is now accessible at:"
echo "http://hrbot.merai.app"
echo ""
echo "You can check the frontend health with:"
echo "curl -I http://hrbot.merai.app"