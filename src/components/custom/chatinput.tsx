import { Textarea } from "../ui/textarea";
import { cx } from 'classix';
import { Button } from "../ui/button";
import { ArrowUpIcon } from "./icons";
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { useState } from 'react';

interface ChatInputProps {
    question: string;
    setQuestion: (question: string) => void;
    onSubmit: (text?: string) => void;
    isLoading: boolean;
    placeholder?: string;  // Adding optional placeholder prop
}

const suggestedActions = [
    {
        title: 'What is the leave policy',
        label: 'for employees?',
        action: 'Can you explain the company leave policy for employees?',
    },
    {
        title: 'Tell me about benefits',
        label: 'and perks',
        action: 'What are the employee benefits and perks offered by the company?',
    },
];

export const ChatInput = ({ question, setQuestion, onSubmit, isLoading, placeholder }: ChatInputProps) => {
    const [showSuggestions, setShowSuggestions] = useState(true);

    return (
        <div className="relative w-full flex flex-col gap-4">
            {showSuggestions && (
                <div className="hidden md:grid sm:grid-cols-2 gap-2 w-full">
                    {suggestedActions.map((suggestedAction, index) => (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: 20 }}
                            transition={{ delay: 0.05 * index }}
                            key={index}
                            className={index > 1 ? 'hidden sm:block' : 'block'}
                        >
                            <Button
                                variant="ghost"
                                onClick={() => {
                                    const text = suggestedAction.action;
                                    onSubmit(text);
                                    setShowSuggestions(false);
                                }}
                                className="text-left border border-gray-300 dark:border-gray-600 rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start bg-transparent hover:bg-gray-100/50 dark:hover:bg-gray-800/30"
                            >
                                <span className="font-medium">{suggestedAction.title}</span>
                                <span className="text-muted-foreground">
                                    {suggestedAction.label}
                                </span>
                            </Button>
                        </motion.div>
                    ))}
                </div>
            )}
            {/* File input retained for potential HR document uploads (e.g., resumes, forms) */}
            <input
                type="file"
                className="fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none"
                multiple
                tabIndex={-1}
            />

            <Textarea
                placeholder={placeholder || "Ask about HR policies, benefits, or more..."}
                className={cx(
                    'min-h-[24px] max-h-[calc(75dvh)] overflow-hidden resize-none rounded-2xl text-base bg-transparent border-gray-300 dark:border-gray-600',
                )}
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                onKeyDown={(event) => {
                    if (event.key === 'Enter' && !event.shiftKey) {
                        event.preventDefault();

                        if (isLoading) {
                            toast.error('Please wait for the HR assistant to respond!');
                        } else {
                            setShowSuggestions(false);
                            onSubmit();
                        }
                    }
                }}
                rows={3}
                autoFocus
            />

            <Button
                className="rounded-full p-2 h-8 w-8 flex items-center justify-center absolute bottom-2 right-2 m-0.5 border border-gray-300 dark:border-gray-600 bg-transparent text-gray-600 dark:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/30 shadow-sm"
                onClick={() => onSubmit(question)}
                disabled={question.length === 0}
            >
                <ArrowUpIcon size={14} />
            </Button>
        </div>
    );
};