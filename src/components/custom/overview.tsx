import { motion } from 'framer-motion';
import { FileText, Calendar, Briefcase } from 'lucide-react';

export const Overview = () => {
  const features = [
    {
      icon: FileText,
      title: "HR Policies",
      description: "Ask about leave, travel, and other company policies",
      iconColor: "text-gray-600 dark:text-gray-400",
      bgColor: "bg-transparent dark:bg-transparent",
      borderColor: "border-gray-300 dark:border-gray-600"
    },
    {
      icon: Calendar,
      title: "Leave Management",
      description: "Learn about different types of leave and eligibility",
      iconColor: "text-gray-600 dark:text-gray-400",
      bgColor: "bg-transparent dark:bg-transparent",
      borderColor: "border-gray-300 dark:border-gray-600"
    },
    {
      icon: Briefcase,
      title: "Workplace Guidelines",
      description: "Information about code of conduct and workplace policies",
      iconColor: "text-gray-600 dark:text-gray-400",
      bgColor: "bg-transparent dark:bg-transparent",
      borderColor: "border-gray-300 dark:border-gray-600"
    }
  ];

  return (
    <motion.div
      key="overview"
      className="max-w-3xl mx-auto pt-6 pb-4"
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.98 }}
      transition={{ delay: 0.3 }}
    >
      <div className="flex flex-col gap-6 leading-relaxed text-center max-w-2xl mx-auto px-4">
        {/* Logo and Title */}
        <div className="flex flex-col items-center justify-center gap-4">
          <div className="p-2">
            <img
              src="/images/Merai _ Redesigning Future .png"
              alt="Merai Logo"
              className="h-24 max-w-full"
            />
          </div>
        </div>

        {/* Welcome Message */}
        <div className="text-center">
          <p className="text-gray-700 dark:text-gray-300">
            Welcome to the <span className="font-semibold">Merai HR Assistant</span>.
            <br />
            I'm here to help you with HR-related questions and policies.
          </p>
        </div>

        {/* Start Instructions */}
        <div className="text-center bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <p className="text-blue-700 dark:text-blue-200 font-medium">
            To start a new conversation:
          </p>
          <p className="text-blue-600 dark:text-blue-300 text-sm mt-2">
            Type "hi" in the chat box below or send any message to begin
          </p>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
              whileHover={{ scale: 1.02 }}
              className={`bg-transparent p-4 rounded-xl flex flex-col items-center border ${feature.borderColor} hover:shadow-md transition-shadow`}
            >
              <div className={`${feature.bgColor} p-2 rounded-full mb-2`}>
                <feature.icon className={`h-6 w-6 ${feature.iconColor}`} />
              </div>
              <h3 className="font-medium text-sm mb-1">{feature.title}</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Additional Instructions */}
        <div className="text-sm text-gray-500 dark:text-gray-400">
          <p>You can restart the conversation anytime using the reset button in the top right corner</p>
        </div>
      </div>
    </motion.div>
  );
};