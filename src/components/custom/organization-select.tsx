import { useState, useEffect } from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface OrganizationSelectProps {
  onSelect: (organization: string) => void;
  apiUrl: string;
}

export function OrganizationSelect({ onSelect, apiUrl }: OrganizationSelectProps) {
  // For debugging
  console.log('OrganizationSelect apiUrl:', apiUrl);
  const [organizations, setOrganizations] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        setIsLoading(true);
        // Use the base URL directly with /organizations
        const organizationsUrl = `${apiUrl.includes('/query') ? apiUrl.replace('/query', '/organizations') : `${apiUrl}/organizations`}`;
        console.log('Fetching organizations from:', organizationsUrl);
        
        const response = await fetch(organizationsUrl);
        if (!response.ok) {
          throw new Error(`Failed to fetch organizations: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        setOrganizations(data.organizations);
      } catch (err) {
        console.error('Error fetching organizations:', err);
        setError(err instanceof Error ? err.message : 'Failed to load organizations');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganizations();
  }, [apiUrl]);

  if (error) {
    return (
      <div className="text-red-500 text-sm">
        Error loading organizations: {error}
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="text-gray-500 text-sm">
        Loading organizations...
      </div>
    );
  }

  return (
    <Select onValueChange={onSelect}>
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select your organization" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Main Organizations</SelectLabel>
          {organizations
            .filter(org => !org.includes("Unit II") && !org.includes("Healthcare") && !org.includes("Diagnostics") && !org.includes("Endo Surgery"))
            .map((org) => (
              <SelectItem key={org} value={org}>
                {org}
              </SelectItem>
            ))}
          <SelectLabel>Subsidiary Organizations</SelectLabel>
          {organizations
            .filter(org => org.includes("Unit II") || org.includes("Healthcare") || org.includes("Diagnostics") || org.includes("Endo Surgery"))
            .map((org) => (
              <SelectItem key={org} value={org}>
                {org}
              </SelectItem>
            ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}