import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cx } from 'classix';
import { useInView } from 'react-intersection-observer';
import { Markdown } from './markdown';
import { MessageActions } from '@/components/custom/actions';
import { BotIcon, UserIcon } from 'lucide-react';
import { message, Button } from '../../interfaces/interfaces';

interface PreviewMessageProps {
  message: message;
  isGrouped?: boolean | null;
  showTimestamp?: boolean;
  onButtonClick?: (payload: string, title: string) => void;
}

export const PreviewMessage = ({
  message,
  isGrouped = false,
  showTimestamp = true,
  onButtonClick
}: PreviewMessageProps) => {
  const [isRead, setIsRead] = useState(message.isRead || false);

  const { ref, inView } = useInView({
    threshold: 0.5,
    triggerOnce: true
  });

  useEffect(() => {
    if (inView && !isRead && message.role === 'assistant') {
      const timer = setTimeout(() => {
        setIsRead(true);
        message.isRead = true;
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [inView, isRead, message]);

  const formatTime = (date?: Date) => {
    if (!date) return '';
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  return (
    <motion.div
      className={cx(
        "w-full mx-auto max-w-3xl px-4 group/message",
        isGrouped ? "mt-1" : "mt-4"
      )}
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      data-role={message.role}
      data-grouped={isGrouped}
    >
      <div className={cx(
        'flex gap-4 w-full py-3',
        message.role === 'user' ? 'justify-end' : 'justify-start'
      )}>
        {/* Avatar */}
        {message.role === 'assistant' && !isGrouped && (
          <div className="flex-shrink-0">
            <div className="size-8 flex items-center rounded-full justify-center bg-transparent border border-gray-300 dark:border-gray-600 shrink-0">
              <BotIcon size={16} className="text-gray-600 dark:text-gray-300" />
            </div>
          </div>
        )}

        {message.role === 'assistant' && isGrouped && (
          <div className="flex-shrink-0 w-8"></div>
        )}

        {/* Message Bubble */}
        <div
          ref={message.role === 'assistant' ? ref : undefined}
          className={cx(
            'flex flex-col max-w-[80%] md:max-w-[70%] px-4 py-3',
            message.role === 'user'
              ? 'bg-transparent border border-gray-300 text-gray-800 ml-auto'
              : 'bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 text-gray-800 dark:text-gray-100 shadow-sm',
            isGrouped
              ? message.role === 'user'
                ? 'rounded-2xl rounded-tr-lg'
                : 'rounded-2xl rounded-tl-lg'
              : message.role === 'user'
                ? 'rounded-2xl rounded-tr-none'
                : 'rounded-2xl rounded-tl-none'
          )}
        >
          {/* Message Content */}
          {message.content && (
            <div className="flex flex-col gap-2 text-left">
              <Markdown>{message.content}</Markdown>
            </div>
          )}

          {/* Message Actions for Assistant */}
          {message.role === 'assistant' && <MessageActions message={message} />}

          {/* Optional Buttons */}
          {message.buttons && message.buttons.length > 0 && (
            <div className="mt-3 flex flex-wrap gap-2">
              {message.buttons.map((btn: Button, index: number) => (
                <button
                  key={index}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm text-gray-800 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition"
                  onClick={() => onButtonClick?.(btn.payload, btn.title)}
                >
                  {btn.title}
                </button>
              ))}
            </div>
          )}

          {/* Timestamp */}
          {showTimestamp && message.timestamp && (
            <div className={cx(
              "text-xs mt-1",
              message.role === 'user' ? "self-end" : "self-start",
              !isRead && message.role === 'assistant' ? "text-gray-600 dark:text-gray-300 font-medium" : "text-gray-400"
            )}>
              {formatTime(message.timestamp)}
              {!isRead && message.role === 'assistant' && (
                <span className="ml-2 inline-block w-2 h-2 bg-gray-500 rounded-full animate-pulse"></span>
              )}
            </div>
          )}
        </div>

        {message.role === 'user' && !isGrouped && (
          <div className="flex-shrink-0">
            <div className="size-8 flex items-center rounded-full justify-center bg-transparent border border-gray-300 dark:border-gray-600 shrink-0">
              <UserIcon size={16} className="text-gray-600 dark:text-gray-400" />
            </div>
          </div>
        )}

        {message.role === 'user' && isGrouped && (
          <div className="flex-shrink-0 w-8"></div>
        )}
      </div>
    </motion.div>
  );
};

// Optional: typing animation
export const ThinkingMessage = () => {
  return (
    <motion.div
      className="w-full mx-auto max-w-3xl px-4 group/message mt-4"
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
      data-role="assistant"
    >
      <div className="flex gap-4 w-full py-3 justify-start">
        <div className="flex-shrink-0">
          <div className="size-8 flex items-center rounded-full justify-center bg-transparent border border-gray-300 dark:border-gray-600 shrink-0">
            <BotIcon size={16} className="text-gray-600 dark:text-gray-300" />
          </div>
        </div>

        <div className="bg-transparent border border-gray-300 dark:border-gray-600 rounded-2xl rounded-tl-none px-4 py-3 flex items-center">
          <div className="flex space-x-2">
            <div className="h-2 w-2 bg-gray-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="h-2 w-2 bg-gray-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="h-2 w-2 bg-gray-500 rounded-full animate-bounce"></div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};
