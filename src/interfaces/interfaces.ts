export interface Button {
  title: string;
  payload: string;
}

export interface message {
  content: string;
  role: 'user' | 'assistant';
  id: string;
  timestamp: Date;
  isRead: boolean;
  sources?: any[];
  buttons?: Button[]; // Adding buttons property
}

export interface ConversationState {
  state: 'INIT' | 'NAME_PENDING' | 'ORG_PENDING' | 'READY';
  userName?: string;
  organization?: string;
}

export interface ChatResponse {
  response: string;
  sources?: any[];
  conversation_id?: string;
  timestamp?: string;
  session_id?: string;
  state?: string;
}
