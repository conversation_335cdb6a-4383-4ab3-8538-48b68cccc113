import { ChatInput } from "@/components/custom/chatinput";
import { PreviewMessage, ThinkingMessage } from "../../components/custom/message";
import { useScrollToBottom } from '@/components/custom/use-scroll-to-bottom';
import { useState, useRef, useEffect } from "react";
import { message, ConversationState, ChatResponse } from "../../interfaces/interfaces"
import { Overview } from "@/components/custom/overview";
import { Header } from "@/components/custom/header";
import { OrganizationSelect } from "@/components/custom/organization-select";
import {v4 as uuidv4} from 'uuid';
import { Button } from "@/components/ui/button";
import { RotateCcw } from 'lucide-react';

// API endpoint configuration
const API_BASE_URL = (window as any).ENV?.API_URL || import.meta.env.VITE_API_URL || "http://localhost:8085";
const API_URL = `${API_BASE_URL}/query`;

export function Chat() {
  const [messagesContainerRef, messagesEndRef] = useScrollToBottom<HTMLDivElement>();
  const [messages, setMessages] = useState<message[]>([]);
  const [question, setQuestion] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [conversationId, setConversationId] = useState<string>(uuidv4());
  const [conversationState, setConversationState] = useState<ConversationState>({
    state: "INIT"
  });

  const handleRestart = async () => {
    try {
      // Call reset endpoint
      const response = await fetch(`${API_BASE_URL}/reset`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversation_id: conversationId
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to reset conversation');
      }

      // Reset local state
      setMessages([]);
      setConversationState({ state: "INIT" });
      setConversationId(uuidv4());
      
      // Send initial message
      handleSubmit("hi");
    } catch (error) {
      console.error('Error resetting conversation:', error);
    }
  };

  const handleButtonClick = (payload: string, title: string) => {
    setQuestion(title);
    handleSubmit(title);
  };

  const handleOrganizationSelect = (organization: string) => {
    handleSubmit(organization);
  };

  async function handleSubmit(text?: string) {
    if (isLoading) return;

    const messageText = text || question;
    if (!messageText.trim()) return;

    setIsLoading(true);
    const messageId = uuidv4();

    // Add user message to the chat
    setMessages(prev => [...prev, {
      content: messageText,
      role: "user",
      id: messageId,
      timestamp: new Date(),
      isRead: true
    }]);
    setQuestion("");

    try {
      // Send message to API
      const response = await fetch(API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: messageText,
          conversation_id: conversationId,
          stream: false,
          session_id: localStorage.getItem('session_id') || undefined
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ChatResponse = await response.json();
      
      // Store session_id in localStorage if provided
      if (data.session_id) {
        localStorage.setItem('session_id', data.session_id);
      }

      // Update conversation state based on response
      if (data.state) {
        // Use the state from the API response if available
        if (data.state === "name_response") {
          setConversationState({ ...conversationState, state: "NAME_PENDING" });
        } else if (data.state === "org_required") {
          setConversationState({ 
            ...conversationState, 
            state: "ORG_PENDING",
            userName: messageText 
          });
        } else if (data.state === "ready") {
          setConversationState({ 
            ...conversationState, 
            state: "READY",
            organization: messageText 
          });
        }
      } else {
        // Fallback to the old text-based detection
        if (conversationState.state === "INIT" && data.response.includes("may I know your name")) {
          setConversationState({ ...conversationState, state: "NAME_PENDING" });
        } else if (conversationState.state === "NAME_PENDING" && data.response.includes("may I know your organization")) {
          setConversationState({ 
            ...conversationState, 
            state: "ORG_PENDING",
            userName: messageText 
          });
        } else if (conversationState.state === "ORG_PENDING" && !data.response.includes("valid organization")) {
          setConversationState({ 
            ...conversationState, 
            state: "READY",
            organization: messageText 
          });
        }
      }

      // Add assistant's response to chat
      const botMessage: message = {
        content: data.response,
        role: "assistant",
        id: uuidv4(),
        timestamp: new Date(),
        isRead: false,
        sources: data.sources
      };
      setMessages(prev => [...prev, botMessage]);

    } catch (error) {
      console.error("API error:", error);
      setMessages(prev => [...prev, {
        content: "Sorry, there was an error processing your request. Please try again later.",
        role: "assistant",
        id: messageId,
        timestamp: new Date(),
        isRead: false
      }]);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex flex-col min-w-0 h-dvh bg-white dark:bg-gray-900">
      <div className="flex justify-between items-center">
        <Header/>
        <Button
          onClick={handleRestart}
          variant="ghost"
          size="icon"
          className="mr-4"
          title="Restart conversation"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
      </div>
      {conversationState.state === "READY" && (
        <div className="bg-blue-50 dark:bg-blue-900/20 px-4 py-2 text-sm text-blue-700 dark:text-blue-200">
          Welcome {conversationState.userName}! You're connected to {conversationState.organization}'s HR assistant.
        </div>
      )}
      <div
        className="flex flex-col min-w-0 gap-1 flex-1 overflow-y-auto pt-2 pb-4 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700"
        ref={messagesContainerRef}
      >
        {messages.length == 0 && <Overview />}
        {messages.map((message, index) => {
          const prevMessage = index > 0 ? messages[index - 1] : null;
          const isGrouped = prevMessage && prevMessage.role === message.role;

          return (
            <PreviewMessage
              key={index}
              message={message}
              isGrouped={isGrouped}
              showTimestamp={!isGrouped || index === messages.length - 1}
              onButtonClick={handleButtonClick}
            />
          );
        })}
        {isLoading && <ThinkingMessage />}
        <div ref={messagesEndRef} className="shrink-0 min-w-[24px] min-h-[24px]"/>
      </div>
      <div className="border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 py-4">
        <div className="flex mx-auto px-4 gap-2 w-full max-w-3xl">
          {conversationState.state === "ORG_PENDING" ? (
            <OrganizationSelect 
              onSelect={handleOrganizationSelect}
              apiUrl={API_BASE_URL}
            />
          ) : (
            <ChatInput
              question={question}
              setQuestion={setQuestion}
              onSubmit={handleSubmit}
              isLoading={isLoading}
              placeholder={
                conversationState.state === "NAME_PENDING"
                  ? "Please enter your name..."
                  : conversationState.state === "READY"
                  ? "Type your message here..."
                  : "Starting conversation..."
              }
            />
          )}
        </div>
      </div>
    </div>
  );
};

